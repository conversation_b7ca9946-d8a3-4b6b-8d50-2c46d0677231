// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase2-ServiceLayer"
//   Timestamp: "2024-12-19T11:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "Aether-Engineering-SOLID-S, Aether-Engineering-Facade-Pattern"
//   Quality_Check: "Service layer providing unified interface for alarm analysis operations."
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Common;
using AlarmAnalysis.DataAccess;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.Services
{
    /// <summary>
    /// 报警分析服务，提供统一的分析操作接口
    /// </summary>
    public class AlarmAnalysisService : IDisposable
    {
        private readonly AlarmDataReader _dataReader;
        private readonly BasicAlarmAnalyzer _analyzer;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AlarmAnalysisService()
        {
            _dataReader = new AlarmDataReader();
            _analyzer = new BasicAlarmAnalyzer();
        }
        
        /// <summary>
        /// 带连接字符串的构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public AlarmAnalysisService(string connectionString)
        {
            _dataReader = new AlarmDataReader(connectionString);
            _analyzer = new BasicAlarmAnalyzer();
        }
        
        #endregion
        
        #region 数据加载
        
        /// <summary>
        /// 加载指定时间范围的报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">表名</param>
        /// <returns>报警事件列表</returns>
        public List<AlarmEvent> LoadAlarmData(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                return _dataReader.ReadAlarmEvents(startTime, endTime, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 异步加载指定时间范围的报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="tableName">表名</param>
        /// <returns>报警事件列表</returns>
        public async Task<List<AlarmEvent>> LoadAlarmDataAsync(DateTime startTime, DateTime endTime, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                return await _dataReader.ReadAlarmEventsAsync(startTime, endTime, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"异步加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 流式加载报警数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="onDataReceived">数据接收回调</param>
        /// <param name="batchSize">批处理大小</param>
        /// <param name="tableName">表名</param>
        public void LoadAlarmDataStream(DateTime startTime, DateTime endTime, 
            Action<List<AlarmEvent>> onDataReceived, int batchSize = 1000, string tableName = "AlarmHistory")
        {
            try
            {
                ValidateTimeRange(startTime, endTime);
                _dataReader.ReadAlarmEventsStream(startTime, endTime, onDataReceived, batchSize, tableName);
            }
            catch (Exception ex)
            {
                throw new DataAccessException($"流式加载报警数据失败: {ex.Message}", ex);
            }
        }
        
        #endregion
        
        #region Phase 2 分析功能
        
        /// <summary>
        /// 执行完整的基础分析
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>完整分析结果</returns>
        public CompleteAnalysisResult PerformCompleteAnalysis(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                if (alarmEvents == null)
                    throw new ArgumentNullException(nameof(alarmEvents));
                
                var result = new CompleteAnalysisResult
                {
                    AnalysisTime = DateTime.Now,
                    DataTimeRange = GetDataTimeRange(alarmEvents),
                    TotalEvents = alarmEvents.Count
                };
                
                // Top N 分析
                result.TopAlarmMessages = _analyzer.GetTopFrequentAlarmMessages(alarmEvents, topN);
                result.TopAlarmingDevices = _analyzer.GetTopAlarmingDevices(alarmEvents, topN);
                result.TopAlarmingStations = _analyzer.GetTopAlarmingStations(alarmEvents, topN);
                
                // 报警率分析
                result.AlarmRates = _analyzer.CalculateAlarmRates(alarmEvents);
                
                // 持续和陈旧报警分析
                result.LongStandingAlarms = _analyzer.IdentifyLongStandingAlarms(alarmEvents);
                result.StaleAlarms = _analyzer.IdentifyStaleAlarms(alarmEvents);
                
                return result;
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"执行完整分析失败: {ex.Message}", ex, "CompleteAnalysis", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取Top N最频繁报警消息
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopFrequentAlarms(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopFrequentAlarmMessages(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"Top N报警分析失败: {ex.Message}", ex, "TopFrequentAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取报警最多的设备
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingDevices(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopAlarmingDevices(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"设备报警分析失败: {ex.Message}", ex, "TopAlarmingDevices", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 获取报警最多的站点
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <param name="topN">Top N数量</param>
        /// <returns>分析结果</returns>
        public List<AlarmFrequencyResult> GetTopAlarmingStations(List<AlarmEvent> alarmEvents, int? topN = null)
        {
            try
            {
                return _analyzer.GetTopAlarmingStations(alarmEvents, topN);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"站点报警分析失败: {ex.Message}", ex, "TopAlarmingStations", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 计算报警率
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>报警率结果</returns>
        public AlarmRateResult CalculateAlarmRates(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.CalculateAlarmRates(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"报警率计算失败: {ex.Message}", ex, "AlarmRates", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 识别长期持续报警
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>长期持续报警列表</returns>
        public List<LongStandingAlarmResult> IdentifyLongStandingAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.IdentifyLongStandingAlarms(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"长期持续报警识别失败: {ex.Message}", ex, "LongStandingAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        /// <summary>
        /// 识别陈旧报警
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>陈旧报警列表</returns>
        public List<StaleAlarmResult> IdentifyStaleAlarms(List<AlarmEvent> alarmEvents)
        {
            try
            {
                return _analyzer.IdentifyStaleAlarms(alarmEvents);
            }
            catch (Exception ex)
            {
                throw new AnalysisException($"陈旧报警识别失败: {ex.Message}", ex, "StaleAlarms", alarmEvents?.Count ?? 0);
            }
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestDatabaseConnection()
        {
            return _dataReader.TestConnection();
        }
        
        /// <summary>
        /// 异步测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            return await _dataReader.TestConnectionAsync();
        }
        
        /// <summary>
        /// 获取记录总数
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>记录总数</returns>
        public long GetRecordCount(string tableName = "AlarmHistory", DateTime? startTime = null, DateTime? endTime = null)
        {
            return _dataReader.GetRecordCount(tableName, startTime, endTime);
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 验证时间范围
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        private static void ValidateTimeRange(DateTime startTime, DateTime endTime)
        {
            if (startTime >= endTime)
            {
                throw new ValidationException("开始时间必须小于结束时间", "TimeRange", $"StartTime: {startTime}, EndTime: {endTime}");
            }
            
            if (endTime > DateTime.Now)
            {
                throw new ValidationException("结束时间不能超过当前时间", "TimeRange", $"EndTime: {endTime}, Now: {DateTime.Now}");
            }
        }
        
        /// <summary>
        /// 获取数据时间范围
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>时间范围字符串</returns>
        private static string GetDataTimeRange(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || !alarmEvents.Any())
                return "无数据";
            
            var minTime = alarmEvents.Min(e => e.EventDateTime);
            var maxTime = alarmEvents.Max(e => e.EventDateTime);
            
            return $"{minTime:yyyy-MM-dd HH:mm:ss} - {maxTime:yyyy-MM-dd HH:mm:ss}";
        }
        
        #endregion
        
        #region IDisposable实现
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _dataReader?.Dispose();
                _disposed = true;
            }
        }
        
        /// <summary>
        /// 析构函数
        /// </summary>
        ~AlarmAnalysisService()
        {
            Dispose(false);
        }
        
        #endregion
    }
    
    /// <summary>
    /// 完整分析结果
    /// </summary>
    public class CompleteAnalysisResult
    {
        public DateTime AnalysisTime { get; set; }
        public string DataTimeRange { get; set; }
        public int TotalEvents { get; set; }
        
        // Phase 2 分析结果
        public List<AlarmFrequencyResult> TopAlarmMessages { get; set; }
        public List<AlarmFrequencyResult> TopAlarmingDevices { get; set; }
        public List<AlarmFrequencyResult> TopAlarmingStations { get; set; }
        public AlarmRateResult AlarmRates { get; set; }
        public List<LongStandingAlarmResult> LongStandingAlarms { get; set; }
        public List<StaleAlarmResult> StaleAlarms { get; set; }
        
        /// <summary>
        /// 获取分析摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine($"分析时间: {AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            summary.AppendLine($"数据时间范围: {DataTimeRange}");
            summary.AppendLine($"总报警数: {TotalEvents:N0}");
            summary.AppendLine($"平均报警率: {AlarmRates?.AlarmsPerHour:F2} 次/小时");
            summary.AppendLine($"峰值报警率: {AlarmRates?.PeakAlarmRate?.MaxAlarmsInWindow:N0} 次/{AlarmRates?.PeakAlarmRate?.WindowSize.TotalMinutes:F0}分钟");
            summary.AppendLine($"长期持续报警: {LongStandingAlarms?.Count:N0} 项");
            summary.AppendLine($"陈旧报警: {StaleAlarms?.Count:N0} 项");
            
            return summary.ToString();
        }
    }
}
// {{END_MODIFICATIONS}}
