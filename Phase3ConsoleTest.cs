// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-ConsoleTest"
//   Timestamp: "2025-01-24T15:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), KISS"
//   Quality_Check: "独立的控制台测试程序，验证Phase 3功能。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// 独立的Phase 3控制台测试程序
    /// </summary>
    class Phase3ConsoleTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Phase 3 Advanced Alarm Analysis Console Test ===\n");
            
            try
            {
                // 生成测试数据
                var testData = GenerateTestData();
                Console.WriteLine($"Generated {testData.Count} test records\n");
                
                // 测试1: 生命周期重构
                Console.WriteLine("Test 1: Lifecycle Reconstruction");
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    Console.WriteLine($"  Generated {lifecycles.Count} lifecycles");
                    
                    var sampleLifecycle = lifecycles.Values.FirstOrDefault();
                    if (sampleLifecycle != null)
                    {
                        Console.WriteLine($"  Sample: {sampleLifecycle.Station}.{sampleLifecycle.Device}");
                        Console.WriteLine($"    TTA: {sampleLifecycle.TimeToAcknowledge?.TotalMinutes:F1} min");
                        Console.WriteLine($"    TTR: {sampleLifecycle.TimeToResolve?.TotalMinutes:F1} min");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试2: KPI计算
                Console.WriteLine("Test 2: KPI Calculation");
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    
                    Console.WriteLine($"  Total lifecycles: {kpis.TotalLifecycles}");
                    Console.WriteLine($"  Acknowledgment rate: {kpis.AcknowledgmentRate:F1}%");
                    Console.WriteLine($"  Resolution rate: {kpis.ResolutionRate:F1}%");
                    Console.WriteLine($"  Avg TTA: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} min");
                    Console.WriteLine($"  Avg TTR: {kpis.TTRStatistics.AverageValue.TotalMinutes:F1} min");
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试3: 抖动检测
                Console.WriteLine("Test 3: Flutter Detection");
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var flutters = analyzer.DetectFlutterAlarms(testData);
                    Console.WriteLine($"  Detected {flutters.Count} flutter alarms");
                    
                    foreach (var flutter in flutters.Take(2))
                    {
                        Console.WriteLine($"    {flutter.Station}.{flutter.Device}: {flutter.ActivationCount} activations");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试4: 瞬时报警检测
                Console.WriteLine("Test 4: Transient Detection");
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);
                    Console.WriteLine($"  Detected {transients.Count} transient alarms");
                    
                    foreach (var transient in transients.Take(2))
                    {
                        Console.WriteLine($"    {transient.Station}.{transient.Device}: {transient.Duration.TotalMinutes:F1} min");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试5: 完整分析
                Console.WriteLine("Test 5: Complete Analysis");
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(testData);
                    Console.WriteLine($"  Analysis completed successfully");
                    Console.WriteLine($"  Lifecycles: {result.AlarmLifecycles.Count}");
                    Console.WriteLine($"  Flutter alarms: {result.FlutterAlarms.Count}");
                    Console.WriteLine($"  Transient alarms: {result.TransientAlarms.Count}");
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 性能测试
                Console.WriteLine("Test 6: Performance Test");
                var largeDataset = GenerateLargeDataset(1000);
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(largeDataset);
                    stopwatch.Stop();
                    
                    Console.WriteLine($"  Processed {largeDataset.Count} records in {stopwatch.ElapsedMilliseconds} ms");
                    Console.WriteLine($"  Speed: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} records/sec");
                    Console.WriteLine($"  Generated {result.AlarmLifecycles.Count} lifecycles");
                }
                Console.WriteLine("  ✓ Passed\n");
                
                Console.WriteLine("=== All Tests Passed Successfully! ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Details: {ex}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static List<AlarmEvent> GenerateTestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-12);
            
            // 生成正常报警序列
            for (int i = 0; i < 10; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 20);
                
                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });
                
                // 确认事件
                if (i % 2 == 0)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(5),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });
                    
                    // 解决事件
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Inactive | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(15),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 2
                    });
                }
            }
            
            // 生成抖动报警
            var flutterTime = baseTime.AddHours(6);
            for (int i = 0; i < 4; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 20),
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }
            
            // 生成瞬时报警
            var transientTime = baseTime.AddHours(8);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });
            
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(1),
                Severity = 100,
                EventSequence = 2001
            });
            
            return testData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        static List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-1);
            
            var stations = new[] { "Station1", "Station2", "Station3" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4" };
            var messages = new[] { "Temp Alarm", "Press Alarm", "Flow Alarm" };
            
            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];
                
                var alarmTime = baseTime.AddMinutes(i * 0.5);
                
                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 2 ? "TestUser" : null
                });
            }
            
            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
// {{END_MODIFICATIONS}}
