// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-Testing"
//   Timestamp: "2025-01-24T14:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), DRY"
//   Quality_Check: "Phase 3功能测试程序，验证生命周期分析、KPI计算和抖动检测。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;
using log4net;
using log4net.Config;
using Newtonsoft.Json;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// Phase 3功能测试程序
    /// 验证报警生命周期分析、响应KPI计算和抖动检测功能
    /// </summary>
    public class Phase3TestProgram
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Phase3TestProgram));
        
        public static void Main(string[] args)
        {
            // 初始化log4net
            XmlConfigurator.Configure();
            
            // 设置控制台编码为UTF-8以正确显示中文
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            Console.WriteLine("=== Phase 3 高级报警分析功能测试 ===\n");
            _logger.Info("Phase 3测试程序启动");
            
            try
            {
                // 测试1: 加载样例数据
                var sampleData = LoadSampleData();
                Console.WriteLine($"✅ 样例数据加载完成，共 {sampleData.Count} 条记录\n");
                
                // 测试2: 生命周期重构
                TestLifecycleReconstruction(sampleData);
                
                // 测试3: 响应KPI计算
                TestResponseKPICalculation(sampleData);
                
                // 测试4: 抖动检测
                TestFlutterDetection(sampleData);
                
                // 测试5: 瞬时报警检测
                TestTransientDetection(sampleData);
                
                // 测试6: 完整Phase 3分析
                TestCompletePhase3Analysis(sampleData);
                
                // 测试7: 性能测试
                TestPerformanceWithLargeDataset();
                
                Console.WriteLine("\n=== Phase 3功能测试完成 ===");
                Console.WriteLine("所有测试通过！✅");
                _logger.Info("Phase 3测试程序完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                _logger.Error($"测试失败: {ex.Message}", ex);
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 加载样例数据
        /// </summary>
        /// <returns>报警事件列表</returns>
        private static List<AlarmEvent> LoadSampleData()
        {
            _logger.Info("开始加载样例数据");
            
            try
            {
                string jsonPath = Path.Combine("Docs", "UFUAAuditLogItem.json");
                if (!File.Exists(jsonPath))
                {
                    _logger.Warn($"样例数据文件不存在: {jsonPath}，使用模拟数据");
                    return GenerateMockData();
                }
                
                string jsonContent = File.ReadAllText(jsonPath, Encoding.UTF8);
                var rawData = JsonConvert.DeserializeObject<List<dynamic>>(jsonContent);
                
                var alarmEvents = new List<AlarmEvent>();
                foreach (var item in rawData)
                {
                    var alarmEvent = new AlarmEvent
                    {
                        EventId = item.EventId?.ToString(),
                        EventType = item.EventType?.ToString(),
                        SourceName = item.SourceName?.ToString(),
                        EventDateTime = DateTime.Parse(item.EventDateTime?.ToString()),
                        EventMessage = item.EventMessage?.ToString(),
                        EventState = item.EventState?.ToString(),
                        Severity = decimal.Parse(item.Severity?.ToString() ?? "0"),
                        EventDetails = item.EventDetails?.ToString(),
                        UserName = item.UserName?.ToString(),
                        EventDuration = double.Parse(item.EventDuration?.ToString() ?? "0"),
                        EventOccurence = decimal.Parse(item.EventOccurence?.ToString() ?? "0"),
                        EventSequence = decimal.Parse(item.EventSequence?.ToString() ?? "0")
                    };
                    
                    alarmEvents.Add(alarmEvent);
                }
                
                _logger.Info($"成功加载 {alarmEvents.Count} 条样例数据");
                return alarmEvents;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载样例数据失败: {ex.Message}", ex);
                Console.WriteLine($"⚠️ 加载样例数据失败，使用模拟数据: {ex.Message}");
                return GenerateMockData();
            }
        }
        
        /// <summary>
        /// 生成模拟数据用于测试
        /// </summary>
        /// <returns>模拟报警事件列表</returns>
        private static List<AlarmEvent> GenerateMockData()
        {
            _logger.Info("生成模拟测试数据");
            
            var mockData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-24);
            
            // 生成正常报警序列
            for (int i = 0; i < 50; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 10 + random.Next(0, 5));
                
                mockData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 5}/Source",
                    EventMessage = $"测试报警消息_{i % 3}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i
                });
                
                // 添加对应的确认和解决事件
                if (i % 3 == 0)
                {
                    mockData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 5}/Source",
                        EventMessage = $"测试报警消息_{i % 3}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(5),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i
                    });
                    
                    mockData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 5}/Source",
                        EventMessage = $"测试报警消息_{i % 3}",
                        EventState = "Inactive | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(15),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i
                    });
                }
            }
            
            // 生成抖动报警数据
            var flutterTime = baseTime.AddHours(12);
            for (int i = 0; i < 5; i++)
            {
                mockData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "抖动测试报警",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 10),
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }
            
            // 生成瞬时报警数据
            var transientTime = baseTime.AddHours(18);
            mockData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "瞬时测试报警",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });
            
            mockData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "瞬时测试报警",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(2),
                Severity = 100,
                EventSequence = 2000
            });
            
            _logger.Info($"生成 {mockData.Count} 条模拟数据");
            return mockData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        /// <summary>
        /// 测试生命周期重构功能
        /// </summary>
        /// <param name="sampleData">样例数据</param>
        private static void TestLifecycleReconstruction(List<AlarmEvent> sampleData)
        {
            Console.WriteLine("🔄 测试1: 报警生命周期重构");
            _logger.Info("开始测试生命周期重构");
            
            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(sampleData);
                    
                    Console.WriteLine($"  ✅ 生成生命周期实例: {lifecycles.Count} 个");
                    
                    // 显示前几个生命周期的详细信息
                    int displayCount = Math.Min(3, lifecycles.Count);
                    foreach (var lifecycle in lifecycles.Values.Take(displayCount))
                    {
                        Console.WriteLine($"    - {lifecycle.Station}.{lifecycle.Device}: {lifecycle.EventMessage}");
                        Console.WriteLine($"      状态转换: {lifecycle.StateTransitions.Count} 次");
                        Console.WriteLine($"      TTA: {lifecycle.TimeToAcknowledge?.TotalMinutes:F1} 分钟");
                        Console.WriteLine($"      TTR: {lifecycle.TimeToResolve?.TotalMinutes:F1} 分钟");
                        Console.WriteLine($"      瞬时报警: {(lifecycle.IsTransientAlarm ? "是" : "否")}");
                    }
                    
                    _logger.Info($"生命周期重构测试完成，生成 {lifecycles.Count} 个实例");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 生命周期重构测试失败: {ex.Message}");
                _logger.Error($"生命周期重构测试失败: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试响应KPI计算功能
        /// </summary>
        /// <param name="sampleData">样例数据</param>
        private static void TestResponseKPICalculation(List<AlarmEvent> sampleData)
        {
            Console.WriteLine("📊 测试2: 响应KPI计算");
            _logger.Info("开始测试响应KPI计算");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(sampleData);
                    var kpiResult = analyzer.CalculateResponseKPIs(lifecycles);

                    Console.WriteLine($"  ✅ KPI计算完成");
                    Console.WriteLine($"    总生命周期: {kpiResult.TotalLifecycles}");
                    Console.WriteLine($"    确认率: {kpiResult.AcknowledgmentRate:F1}%");
                    Console.WriteLine($"    解决率: {kpiResult.ResolutionRate:F1}%");
                    Console.WriteLine($"    平均TTA: {kpiResult.TTAStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    Console.WriteLine($"    平均TTR: {kpiResult.TTRStatistics.AverageValue.TotalMinutes:F1} 分钟");
                    Console.WriteLine($"    TTA中位数: {kpiResult.TTAStatistics.MedianValue.TotalMinutes:F1} 分钟");
                    Console.WriteLine($"    TTR中位数: {kpiResult.TTRStatistics.MedianValue.TotalMinutes:F1} 分钟");

                    _logger.Info($"KPI计算测试完成 - {kpiResult.GetSummary()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ KPI计算测试失败: {ex.Message}");
                _logger.Error($"KPI计算测试失败: {ex.Message}", ex);
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试抖动检测功能
        /// </summary>
        /// <param name="sampleData">样例数据</param>
        private static void TestFlutterDetection(List<AlarmEvent> sampleData)
        {
            Console.WriteLine("⚡ 测试3: 抖动报警检测");
            _logger.Info("开始测试抖动检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var flutterResults = analyzer.DetectFlutterAlarms(sampleData);

                    Console.WriteLine($"  ✅ 检测到抖动报警: {flutterResults.Count} 个");

                    foreach (var flutter in flutterResults)
                    {
                        Console.WriteLine($"    - {flutter.GetSummary()}");
                    }

                    _logger.Info($"抖动检测测试完成，发现 {flutterResults.Count} 个抖动实例");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 抖动检测测试失败: {ex.Message}");
                _logger.Error($"抖动检测测试失败: {ex.Message}", ex);
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试瞬时报警检测功能
        /// </summary>
        /// <param name="sampleData">样例数据</param>
        private static void TestTransientDetection(List<AlarmEvent> sampleData)
        {
            Console.WriteLine("⚡ 测试4: 瞬时报警检测");
            _logger.Info("开始测试瞬时报警检测");

            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(sampleData);
                    var transientResults = analyzer.DetectTransientAlarms(lifecycles);

                    Console.WriteLine($"  ✅ 检测到瞬时报警: {transientResults.Count} 个");

                    foreach (var transient in transientResults)
                    {
                        Console.WriteLine($"    - {transient.GetSummary()}");
                    }

                    _logger.Info($"瞬时报警检测测试完成，发现 {transientResults.Count} 个瞬时报警");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 瞬时报警检测测试失败: {ex.Message}");
                _logger.Error($"瞬时报警检测测试失败: {ex.Message}", ex);
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试完整Phase 3分析
        /// </summary>
        /// <param name="sampleData">样例数据</param>
        private static void TestCompletePhase3Analysis(List<AlarmEvent> sampleData)
        {
            Console.WriteLine("🔍 测试5: 完整Phase 3分析");
            _logger.Info("开始测试完整Phase 3分析");

            try
            {
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(sampleData);

                    Console.WriteLine($"  ✅ 完整分析完成");
                    Console.WriteLine(result.GetSummary());

                    _logger.Info($"完整Phase 3分析测试完成");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 完整分析测试失败: {ex.Message}");
                _logger.Error($"完整分析测试失败: {ex.Message}", ex);
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        private static void TestPerformanceWithLargeDataset()
        {
            Console.WriteLine("🚀 测试6: 大数据集性能测试");
            _logger.Info("开始大数据集性能测试");

            try
            {
                // 生成大量测试数据
                var largeDataset = GenerateLargeDataset(10000);
                Console.WriteLine($"  生成测试数据: {largeDataset.Count:N0} 条");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(largeDataset);

                    stopwatch.Stop();

                    Console.WriteLine($"  ✅ 大数据集分析完成");
                    Console.WriteLine($"    处理时间: {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                    Console.WriteLine($"    处理速度: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} 条/秒");
                    Console.WriteLine($"    生命周期: {result.AlarmLifecycles.Count:N0} 个");
                    Console.WriteLine($"    抖动报警: {result.FlutterAlarms.Count:N0} 个");
                    Console.WriteLine($"    瞬时报警: {result.TransientAlarms.Count:N0} 个");

                    _logger.Info($"性能测试完成 - 处理 {largeDataset.Count:N0} 条数据，耗时 {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 性能测试失败: {ex.Message}");
                _logger.Error($"性能测试失败: {ex.Message}", ex);
                throw;
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 生成大数据集用于性能测试
        /// </summary>
        /// <param name="count">数据条数</param>
        /// <returns>大数据集</returns>
        private static List<AlarmEvent> GenerateLargeDataset(int count)
        {
            _logger.Info($"生成大数据集，目标数量: {count:N0}");

            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-30);

            var stations = new[] { "Station1", "Station2", "Station3", "Station4", "Station5" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5", "Device6", "Device7", "Device8", "Device9", "Device10" };
            var messages = new[] { "温度报警", "压力报警", "流量报警", "液位报警", "振动报警", "电流报警", "电压报警" };

            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];

                var alarmTime = baseTime.AddMinutes(i * 0.1 + random.Next(0, 60));

                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 7 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }

            _logger.Info($"大数据集生成完成，实际数量: {dataset.Count:N0}");
            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
// {{END_MODIFICATIONS}}
