﻿using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.UserSkins;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using AlarmAnalysis.Testing;

namespace AlarmAnalysis
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            // 检查是否有命令行参数
            if (args.Length > 0 && args[0] == "Phase3TestProgram")
            {
                // 运行Phase 3测试程序
                SimplePhase3Test.RunTest();
                return;
            }

            // 默认运行GUI应用程序
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new Form1());
        }
    }
}
