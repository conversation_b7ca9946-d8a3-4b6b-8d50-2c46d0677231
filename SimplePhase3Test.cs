// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-SimpleTest"
//   Timestamp: "2025-01-24T15:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), KISS"
//   Quality_Check: "简化的Phase 3功能测试程序，不依赖外部JSON库。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;
using log4net;
using log4net.Config;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// 简化的Phase 3功能测试程序
    /// </summary>
    public class SimplePhase3Test
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SimplePhase3Test));
        
        public static void RunTest()
        {
            // 初始化log4net
            XmlConfigurator.Configure();
            
            // 设置控制台编码
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            Console.WriteLine("=== Phase 3 Advanced Alarm Analysis Test ===\n");
            _logger.Info("Phase 3 test program started");
            
            try
            {
                // 生成测试数据
                var testData = GenerateTestData();
                Console.WriteLine($"Generated test data: {testData.Count} records\n");
                
                // 测试生命周期重构
                TestLifecycleReconstruction(testData);
                
                // 测试KPI计算
                TestKPICalculation(testData);
                
                // 测试抖动检测
                TestFlutterDetection(testData);
                
                // 测试完整分析
                TestCompleteAnalysis(testData);
                
                // 性能测试
                TestPerformance();
                
                Console.WriteLine("\n=== Phase 3 Test Completed Successfully ===");
                Console.WriteLine("All tests passed! ✓");
                _logger.Info("Phase 3 test program completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                _logger.Error($"Test failed: {ex.Message}", ex);
                Console.WriteLine($"Details: {ex}");
            }
        }
        
        private static List<AlarmEvent> GenerateTestData()
        {
            _logger.Info("Generating test data");
            
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-24);
            
            // 生成正常报警序列
            for (int i = 0; i < 20; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 15 + random.Next(0, 5));
                
                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm Message {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });
                
                // 确认事件
                if (i % 2 == 0)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm Message {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(5),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });
                    
                    // 解决事件
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm Message {i % 2}",
                        EventState = "Inactive | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(20),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 2
                    });
                }
            }
            
            // 生成抖动报警
            var flutterTime = baseTime.AddHours(12);
            for (int i = 0; i < 5; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Test Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 15),
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }
            
            // 生成瞬时报警
            var transientTime = baseTime.AddHours(18);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Test Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });
            
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Test Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(2),
                Severity = 100,
                EventSequence = 2001
            });
            
            _logger.Info($"Generated {testData.Count} test records");
            return testData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        private static void TestLifecycleReconstruction(List<AlarmEvent> testData)
        {
            Console.WriteLine("🔄 Test 1: Alarm Lifecycle Reconstruction");
            _logger.Info("Testing lifecycle reconstruction");
            
            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    
                    Console.WriteLine($"  ✓ Generated lifecycles: {lifecycles.Count}");
                    
                    foreach (var lifecycle in lifecycles.Values.Take(3))
                    {
                        Console.WriteLine($"    - {lifecycle.Station}.{lifecycle.Device}: {lifecycle.EventMessage}");
                        Console.WriteLine($"      State transitions: {lifecycle.StateTransitions.Count}");
                        Console.WriteLine($"      TTA: {lifecycle.TimeToAcknowledge?.TotalMinutes:F1} minutes");
                        Console.WriteLine($"      TTR: {lifecycle.TimeToResolve?.TotalMinutes:F1} minutes");
                        Console.WriteLine($"      Transient: {(lifecycle.IsTransientAlarm ? "Yes" : "No")}");
                    }
                    
                    _logger.Info($"Lifecycle reconstruction test completed, generated {lifecycles.Count} instances");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Lifecycle reconstruction test failed: {ex.Message}");
                _logger.Error($"Lifecycle reconstruction test failed: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }
        
        private static void TestKPICalculation(List<AlarmEvent> testData)
        {
            Console.WriteLine("📊 Test 2: Response KPI Calculation");
            _logger.Info("Testing KPI calculation");
            
            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpiResult = analyzer.CalculateResponseKPIs(lifecycles);
                    
                    Console.WriteLine($"  ✓ KPI calculation completed");
                    Console.WriteLine($"    Total lifecycles: {kpiResult.TotalLifecycles}");
                    Console.WriteLine($"    Acknowledgment rate: {kpiResult.AcknowledgmentRate:F1}%");
                    Console.WriteLine($"    Resolution rate: {kpiResult.ResolutionRate:F1}%");
                    Console.WriteLine($"    Average TTA: {kpiResult.TTAStatistics.AverageValue.TotalMinutes:F1} minutes");
                    Console.WriteLine($"    Average TTR: {kpiResult.TTRStatistics.AverageValue.TotalMinutes:F1} minutes");
                    
                    _logger.Info($"KPI calculation test completed - {kpiResult.GetSummary()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ KPI calculation test failed: {ex.Message}");
                _logger.Error($"KPI calculation test failed: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }
        
        private static void TestFlutterDetection(List<AlarmEvent> testData)
        {
            Console.WriteLine("⚡ Test 3: Flutter Alarm Detection");
            _logger.Info("Testing flutter detection");
            
            try
            {
                using (var analyzer = new AdvancedAlarmAnalyzer())
                {
                    var flutterResults = analyzer.DetectFlutterAlarms(testData);
                    
                    Console.WriteLine($"  ✓ Detected flutter alarms: {flutterResults.Count}");
                    
                    foreach (var flutter in flutterResults)
                    {
                        Console.WriteLine($"    - {flutter.GetSummary()}");
                    }
                    
                    _logger.Info($"Flutter detection test completed, found {flutterResults.Count} flutter instances");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Flutter detection test failed: {ex.Message}");
                _logger.Error($"Flutter detection test failed: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }
        
        private static void TestCompleteAnalysis(List<AlarmEvent> testData)
        {
            Console.WriteLine("🔍 Test 4: Complete Phase 3 Analysis");
            _logger.Info("Testing complete Phase 3 analysis");
            
            try
            {
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(testData);
                    
                    Console.WriteLine($"  ✓ Complete analysis finished");
                    Console.WriteLine(result.GetSummary());
                    
                    _logger.Info($"Complete Phase 3 analysis test completed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Complete analysis test failed: {ex.Message}");
                _logger.Error($"Complete analysis test failed: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }
        
        private static void TestPerformance()
        {
            Console.WriteLine("🚀 Test 5: Performance Test");
            _logger.Info("Starting performance test");
            
            try
            {
                var largeDataset = GenerateLargeDataset(5000);
                Console.WriteLine($"  Generated test data: {largeDataset.Count:N0} records");
                
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                using (var service = new AlarmAnalysisService())
                {
                    var result = service.PerformPhase3Analysis(largeDataset);
                    
                    stopwatch.Stop();
                    
                    Console.WriteLine($"  ✓ Large dataset analysis completed");
                    Console.WriteLine($"    Processing time: {stopwatch.ElapsedMilliseconds:N0} ms");
                    Console.WriteLine($"    Processing speed: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} records/sec");
                    Console.WriteLine($"    Lifecycles: {result.AlarmLifecycles.Count:N0}");
                    Console.WriteLine($"    Flutter alarms: {result.FlutterAlarms.Count:N0}");
                    Console.WriteLine($"    Transient alarms: {result.TransientAlarms.Count:N0}");
                    
                    _logger.Info($"Performance test completed - processed {largeDataset.Count:N0} records in {stopwatch.ElapsedMilliseconds:N0} ms");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Performance test failed: {ex.Message}");
                _logger.Error($"Performance test failed: {ex.Message}", ex);
                throw;
            }
            
            Console.WriteLine();
        }
        
        private static List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-7);
            
            var stations = new[] { "Station1", "Station2", "Station3" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm" };
            
            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];
                
                var alarmTime = baseTime.AddMinutes(i * 0.1 + random.Next(0, 30));
                
                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 7 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }
            
            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
// {{END_MODIFICATIONS}}
