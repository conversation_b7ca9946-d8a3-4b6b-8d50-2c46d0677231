// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "Phase3-SimpleConsoleTest"
//   Timestamp: "2025-01-24T16:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), KISS"
//   Quality_Check: "最简化的Phase 3控制台测试程序，不依赖log4net。"
// }}
// {{START_MODIFICATIONS}}
using System;
using System.Collections.Generic;
using System.Linq;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// 最简化的Phase 3控制台测试程序
    /// </summary>
    class SimplePhase3ConsoleTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Phase 3 Advanced Alarm Analysis Test ===\n");
            
            try
            {
                // 生成测试数据
                var testData = GenerateTestData();
                Console.WriteLine($"Generated {testData.Count} test records\n");
                
                // 测试1: 生命周期重构
                Console.WriteLine("Test 1: Lifecycle Reconstruction");
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3)) // 使用参数构造函数避免配置依赖
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    Console.WriteLine($"  Generated {lifecycles.Count} lifecycles");
                    
                    var sampleLifecycle = lifecycles.Values.FirstOrDefault();
                    if (sampleLifecycle != null)
                    {
                        Console.WriteLine($"  Sample: {sampleLifecycle.Station}.{sampleLifecycle.Device}");
                        Console.WriteLine($"    TTA: {sampleLifecycle.TimeToAcknowledge?.TotalMinutes:F1} min");
                        Console.WriteLine($"    TTR: {sampleLifecycle.TimeToResolve?.TotalMinutes:F1} min");
                        Console.WriteLine($"    Transient: {sampleLifecycle.IsTransientAlarm}");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试2: KPI计算
                Console.WriteLine("Test 2: KPI Calculation");
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    
                    Console.WriteLine($"  Total lifecycles: {kpis.TotalLifecycles}");
                    Console.WriteLine($"  Acknowledgment rate: {kpis.AcknowledgmentRate:F1}%");
                    Console.WriteLine($"  Resolution rate: {kpis.ResolutionRate:F1}%");
                    Console.WriteLine($"  Avg TTA: {kpis.TTAStatistics.AverageValue.TotalMinutes:F1} min");
                    Console.WriteLine($"  Avg TTR: {kpis.TTRStatistics.AverageValue.TotalMinutes:F1} min");
                    Console.WriteLine($"  TTA Median: {kpis.TTAStatistics.MedianValue.TotalMinutes:F1} min");
                    Console.WriteLine($"  TTR Median: {kpis.TTRStatistics.MedianValue.TotalMinutes:F1} min");
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试3: 抖动检测
                Console.WriteLine("Test 3: Flutter Detection");
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var flutters = analyzer.DetectFlutterAlarms(testData);
                    Console.WriteLine($"  Detected {flutters.Count} flutter alarms");
                    
                    foreach (var flutter in flutters.Take(3))
                    {
                        Console.WriteLine($"    {flutter.Station}.{flutter.Device}: {flutter.ActivationCount} activations in {flutter.WindowSize.TotalSeconds}s");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 测试4: 瞬时报警检测
                Console.WriteLine("Test 4: Transient Detection");
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(testData);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);
                    Console.WriteLine($"  Detected {transients.Count} transient alarms");
                    
                    foreach (var transient in transients.Take(3))
                    {
                        Console.WriteLine($"    {transient.Station}.{transient.Device}: {transient.Duration.TotalMinutes:F1} min duration");
                    }
                }
                Console.WriteLine("  ✓ Passed\n");
                
                // 性能测试
                Console.WriteLine("Test 5: Performance Test");
                var largeDataset = GenerateLargeDataset(2000);
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                using (var analyzer = new AdvancedAlarmAnalyzer(60, 3))
                {
                    var lifecycles = analyzer.ReconstructAlarmLifecycles(largeDataset);
                    var kpis = analyzer.CalculateResponseKPIs(lifecycles);
                    var flutters = analyzer.DetectFlutterAlarms(largeDataset);
                    var transients = analyzer.DetectTransientAlarms(lifecycles);
                    
                    stopwatch.Stop();
                    
                    Console.WriteLine($"  Processed {largeDataset.Count} records in {stopwatch.ElapsedMilliseconds} ms");
                    Console.WriteLine($"  Speed: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} records/sec");
                    Console.WriteLine($"  Generated {lifecycles.Count} lifecycles");
                    Console.WriteLine($"  Found {flutters.Count} flutter alarms");
                    Console.WriteLine($"  Found {transients.Count} transient alarms");
                }
                Console.WriteLine("  ✓ Passed\n");
                
                Console.WriteLine("=== All Tests Passed Successfully! ===");
                Console.WriteLine("Phase 3 implementation is working correctly.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static List<AlarmEvent> GenerateTestData()
        {
            var testData = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddHours(-12);
            
            // 生成正常报警序列
            for (int i = 0; i < 15; i++)
            {
                var alarmTime = baseTime.AddMinutes(i * 20);
                
                // 激活事件
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"TestStation/Device_{i % 3}/Source",
                    EventMessage = $"Test Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = 100,
                    EventSequence = i * 3
                });
                
                // 确认事件 (70%的概率)
                if (random.Next(10) < 7)
                {
                    testData.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"TestStation/Device_{i % 3}/Source",
                        EventMessage = $"Test Alarm {i % 2}",
                        EventState = "Active | Acknowledged",
                        EventDateTime = alarmTime.AddMinutes(random.Next(2, 8)),
                        Severity = 100,
                        UserName = "TestUser",
                        EventSequence = i * 3 + 1
                    });
                    
                    // 解决事件 (80%的概率)
                    if (random.Next(10) < 8)
                    {
                        testData.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"TestStation/Device_{i % 3}/Source",
                            EventMessage = $"Test Alarm {i % 2}",
                            EventState = "Inactive | Acknowledged",
                            EventDateTime = alarmTime.AddMinutes(random.Next(10, 25)),
                            Severity = 100,
                            UserName = "TestUser",
                            EventSequence = i * 3 + 2
                        });
                    }
                }
            }
            
            // 生成抖动报警 (在60秒内激活4次)
            var flutterTime = baseTime.AddHours(6);
            for (int i = 0; i < 4; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = "TestStation/FlutterDevice/Source",
                    EventMessage = "Flutter Alarm",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = flutterTime.AddSeconds(i * 15), // 每15秒激活一次
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }
            
            // 生成瞬时报警 (直接从Active变为Inactive)
            var transientTime = baseTime.AddHours(8);
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Active | Unacknowledged",
                EventDateTime = transientTime,
                Severity = 100,
                EventSequence = 2000
            });
            
            testData.Add(new AlarmEvent
            {
                EventId = Guid.NewGuid().ToString(),
                SourceName = "TestStation/TransientDevice/Source",
                EventMessage = "Transient Alarm",
                EventState = "Inactive | Unacknowledged",
                EventDateTime = transientTime.AddMinutes(1.5), // 1.5分钟后直接变为Inactive
                Severity = 100,
                EventSequence = 2001
            });
            
            return testData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        static List<AlarmEvent> GenerateLargeDataset(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-2);
            
            var stations = new[] { "Station1", "Station2", "Station3", "Station4" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm" };
            
            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];
                
                var alarmTime = baseTime.AddMinutes(i * 0.5 + random.Next(0, 30));
                
                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = random.Next(10) < 8 ? "Active | Unacknowledged" : "Inactive | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i,
                    UserName = random.Next(10) < 3 ? "TestUser" : null
                });
            }
            
            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
// {{END_MODIFICATIONS}}
