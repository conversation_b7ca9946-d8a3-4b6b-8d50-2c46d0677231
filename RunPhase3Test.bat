@echo off
chcp 65001 >nul
echo ========================================
echo Phase 3 Advanced Alarm Analysis Test
echo ========================================
echo.

echo Creating Logs directory...
if not exist "Logs" mkdir Logs

echo Building simple console test project...
dotnet build SimplePhase3Test.csproj --configuration Release --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please check code errors.
    pause
    exit /b 1
)

echo Build successful!
echo.

echo Running Phase 3 simple console test...
echo.

bin\Release\SimplePhase3Test.exe

echo.
echo Test completed!
echo.
pause
