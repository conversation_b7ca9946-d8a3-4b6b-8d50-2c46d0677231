@echo off
echo ========================================
echo Phase 3 高级报警分析功能测试
echo ========================================
echo.

echo 正在编译项目...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！请检查代码错误。
    pause
    exit /b 1
)

echo 编译成功！
echo.

echo 正在运行Phase 3测试程序...
echo.

dotnet run --project . --configuration Release -- Phase3TestProgram

echo.
echo 测试完成！
echo 请查看生成的日志文件：Logs/AlarmAnalysis.log
echo.
pause
